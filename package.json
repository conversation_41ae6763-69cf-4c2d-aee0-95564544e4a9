{"name": "reapers-riddle", "version": "0.1.0", "private": true, "description": "Reaper's Riddle – a dark, cinematic PWA riddle game", "scripts": {"dev": "node scripts/dev.js", "build": "next build", "start": "next start", "lint": "next lint", "test": "echo 'No tests'"}, "dependencies": {"@ducanh2912/next-pwa": "^10.2.9", "dexie": "3.2.4", "framer-motion": "10.16.1", "next": "^15.5.2", "react": "18.2.0", "react-dom": "18.2.0", "zustand": "4.5.0"}, "devDependencies": {"@types/react": "19.1.12", "autoprefixer": "10.4.16", "eslint": "^8.57.0", "eslint-config-next": "15.0.0", "postcss": "8.4.31", "tailwindcss": "3.3.3", "typescript": "5.2.2"}}