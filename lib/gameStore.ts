'use client';

import { create } from 'zustand';
import { riddles, Riddle } from './riddles';
import { updateStats, addHistory } from './storage';

// Helper to pick a random riddle
const pickRiddle = (): Riddle => riddles[Math.floor(Math.random() * riddles.length)];

// Starting time in hours
const START_HOURS = 24;

export interface GameState {
  hours: number;
  riddle: Riddle;
  guessed: string[];
  wrong: string[];
  solved: number;
  yearsEarned: number;
  status: 'intro' | 'playing' | 'lost';
  start: () => void;
  guess: (letter: string) => void;
  reset: () => void;
}

// Compute extra years from solved riddles
const yearsFromSolved = (solved: number) => {
  if (solved >= 15) return 3;
  if (solved >= 10) return 2;
  if (solved >= 5) return 1;
  return 0;
};

export const useGameStore = create<GameState>()((set, get) => ({
  hours: START_HOURS,
  riddle: riddles[0], // Use first riddle as default instead of random
  guessed: [],
  wrong: [],
  solved: 0,
  yearsEarned: 0,
  status: 'intro',
  start: () => set({ status: 'playing' }),
  guess: (l: string) => {
    const state = get();
    if (state.status !== 'playing' || state.guessed.includes(l) || state.wrong.includes(l)) return;
    const letter = l.toUpperCase();
    const answer = state.riddle.answer.toUpperCase();
    if (answer.includes(letter)) {
      const guessed = [...state.guessed, letter];
      const answerLetters = answer.replace(/\s/g, '').split('');
      const solved = answerLetters.every((ch) => guessed.includes(ch));
      if (solved) {
        const solvedCount = state.solved + 1;
        const yearsEarned = yearsFromSolved(solvedCount);
        // update persistent stats when a riddle is solved
        updateStats((s) => {
          const diff = yearsEarned - state.yearsEarned;
          return {
            ...s,
            riddlesSolved: s.riddlesSolved + 1,
            currentStreak: s.currentStreak + 1,
            bestStreak: Math.max(s.bestStreak, s.currentStreak + 1),
            yearsEarned: s.yearsEarned + (diff > 0 ? diff : 0),
          };
        });
        // record history of the solved riddle
        addHistory({ riddleId: state.riddle.id, wrongGuesses: state.wrong.length, solved: true, date: new Date() });
        set({
          guessed: [],
          wrong: [],
          riddle: pickRiddle(),
          solved: solvedCount,
          yearsEarned,
        });
      } else {
        set({ guessed });
      }
    } else {
      const wrong = [...state.wrong, letter];
      const hours = state.hours - 1; // each wrong letter costs an hour
      updateStats((s) => ({ ...s, wrongGuesses: s.wrongGuesses + 1 }));
      if (hours <= 0) {
        // game over: persist stats and history
        updateStats((s) => ({ ...s, gamesPlayed: s.gamesPlayed + 1, currentStreak: 0, yearsEarned: s.yearsEarned + state.yearsEarned }));
        addHistory({ riddleId: state.riddle.id, wrongGuesses: wrong.length, solved: false, date: new Date() });
        set({ wrong, hours, status: 'lost' });
      } else {
        set({ wrong, hours });
      }
    }
  },
  reset: () =>
    set({
      hours: START_HOURS,
      riddle: pickRiddle(), // Random selection happens here during reset
      guessed: [],
      wrong: [],
      solved: 0,
      yearsEarned: 0,
      status: 'playing',
    }),
}));
