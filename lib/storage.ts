import Dexie, { Table } from 'dexie';

export interface Stats {
  id: number;
  gamesPlayed: number;
  riddlesSolved: number;
  wrongGuesses: number;
  yearsEarned: number;
  currentStreak: number;
  bestStreak: number;
}

export interface HistoryEntry {
  id?: number;
  riddleId: string;
  wrongGuesses: number;
  solved: boolean;
  date: Date;
}

class RiddleDB extends Dexie {
  stats!: Table<Stats, number>;
  history!: Table<HistoryEntry, number>;

  constructor() {
    super('reapers-riddle');
    this.version(1).stores({
      stats: 'id',
      history: '++id, date'
    });
  }
}

export const db = new RiddleDB();

const defaultStats: Stats = {
  id: 1,
  gamesPlayed: 0,
  riddlesSolved: 0,
  wrongGuesses: 0,
  yearsEarned: 0,
  currentStreak: 0,
  bestStreak: 0,
};

export async function getStats(): Promise<Stats> {
  return (await db.stats.get(1)) ?? defaultStats;
}

export async function updateStats(update: (s: Stats) => Stats) {
  const current = await getStats();
  const updated = update(current);
  await db.stats.put(updated);
  return updated;
}

export async function addHistory(entry: Omit<HistoryEntry, 'id'>) {
  return db.history.add(entry);
}
