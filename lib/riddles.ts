export interface Riddle {
  id: string;
  prompt: string;
  answer: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

export const riddles: Riddle[] = [
  // Easy
  { id: 'e1', prompt: 'I speak without a mouth and hear without ears. What am I?', answer: 'echo', difficulty: 'easy' },
  { id: 'e2', prompt: 'What has to be broken before you can use it?', answer: 'egg', difficulty: 'easy' },
  { id: 'e3', prompt: 'What month of the year has 28 days?', answer: 'all', difficulty: 'easy' },
  { id: 'e4', prompt: 'What goes up but never comes down?', answer: 'age', difficulty: 'easy' },
  { id: 'e5', prompt: 'What gets wet while drying?', answer: 'towel', difficulty: 'easy' },
  // Medium
  { id: 'm1', prompt: 'The more of this there is, the less you see. What is it?', answer: 'darkness', difficulty: 'medium' },
  { id: 'm2', prompt: 'I have keys but no locks and space but no rooms. What am I?', answer: 'keyboard', difficulty: 'medium' },
  { id: 'm3', prompt: 'What can travel around the world while staying in a corner?', answer: 'stamp', difficulty: 'medium' },
  { id: 'm4', prompt: 'What has many teeth but cannot bite?', answer: 'comb', difficulty: 'medium' },
  { id: 'm5', prompt: 'I am an odd number. Take away a letter and I become even. What number am I?', answer: 'seven', difficulty: 'medium' },
  // Hard
  { id: 'h1', prompt: 'I have cities but no houses, mountains but no trees, and water but no fish. What am I?', answer: 'map', difficulty: 'hard' },
  { id: 'h2', prompt: 'What walks on four legs in the morning, two in the afternoon and three in the evening?', answer: 'man', difficulty: 'hard' },
  { id: 'h3', prompt: 'The person who makes it, sells it. The person who buys it never uses it. What is it?', answer: 'coffin', difficulty: 'hard' },
  { id: 'h4', prompt: 'I can only live where there is light, but I die if the light shines on me. What am I?', answer: 'shadow', difficulty: 'hard' },
  { id: 'h5', prompt: 'What has one eye but cannot see?', answer: 'needle', difficulty: 'hard' },
];
