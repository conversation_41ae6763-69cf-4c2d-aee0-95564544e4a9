'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useEffect, useState, useRef } from 'react';
import Link from 'next/link';
import { useGameStore } from '@/lib/gameStore';

const message = 'Welcome, mortal. Answer my riddles and steal back your years.';

export default function ReaperIntro() {
  const [text, setText] = useState('');
  const [videoEnded, setVideoEnded] = useState(false);
  const [showContent, setShowContent] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const reset = useGameStore((s) => s.reset);

  useEffect(() => {
    // Start showing content after a brief delay
    const contentTimer = setTimeout(() => {
      setShowContent(true);
    }, 1000);

    // Start typewriter effect after content appears
    const typewriterTimer = setTimeout(() => {
      let i = 0;
      const t = setInterval(() => {
        setText(message.slice(0, i));
        i++;
        if (i > message.length) clearInterval(t);
      }, 60);
      return () => clearInterval(t);
    }, 2000);

    return () => {
      clearTimeout(contentTimer);
      clearTimeout(typewriterTimer);
    };
  }, []);

  const handleVideoEnd = () => {
    setVideoEnded(true);
  };

  const startGame = () => {
    reset();
  };

  return (
    <div className="relative min-h-screen overflow-hidden bg-black">
      {/* Video Background */}
      <AnimatePresence>
        {!videoEnded && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 1 }}
            style={{ position: 'absolute', inset: 0, width: '100%', height: '100%' }}
          >
            <video
              ref={videoRef}
              autoPlay
              muted
              playsInline
              onEnded={handleVideoEnd}
              className="w-full h-full object-cover"
            >
              <source src="/clips/Intro_Dark_Forest.mp4" type="video/mp4" />
            </video>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Static Image Background (appears when video ends) */}
      <AnimatePresence>
        {videoEnded && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 2 }}
            style={{
              position: 'absolute',
              inset: 0,
              width: '100%',
              height: '100%',
              backgroundImage: 'url(/images/dark_forest.png)',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat'
            }}
          />
        )}
      </AnimatePresence>

      {/* Dark Overlay */}
      <div className="absolute inset-0 bg-black/40" />

      {/* Content Overlay */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen text-center p-4">
        <AnimatePresence>
          {showContent && (
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1.5, ease: "easeOut" }}
              style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '2rem', maxWidth: '56rem' }}
            >
              {/* Game Title */}
              <motion.h1
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.5, duration: 1 }}
                style={{
                  fontSize: 'clamp(3rem, 8vw, 6rem)',
                  fontWeight: 'bold',
                  color: '#ef4444',
                  marginBottom: '1rem',
                  letterSpacing: '0.1em',
                  fontFamily: 'serif',
                  textShadow: '0 0 20px rgba(239, 68, 68, 0.5), 0 0 40px rgba(239, 68, 68, 0.3)',
                  filter: 'drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.8))'
                }}
              >
                REAPER'S RIDDLE
              </motion.h1>

              {/* Typewriter Message */}
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1.5, duration: 1 }}
                style={{
                  fontSize: 'clamp(1.25rem, 3vw, 1.875rem)',
                  maxWidth: '48rem',
                  color: '#f3f4f6',
                  lineHeight: '1.6',
                  fontWeight: '500',
                  fontFamily: 'serif',
                  textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)',
                  letterSpacing: '0.05em'
                }}
              >
                {text}
                <motion.span
                  animate={{ opacity: [1, 0] }}
                  transition={{ duration: 0.8, repeat: Infinity, repeatType: "reverse" }}
                  style={{ color: '#f87171' }}
                >
                  |
                </motion.span>
              </motion.p>

              {/* Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 3, duration: 1 }}
                style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '1rem', marginTop: '2rem' }}
              >
                <Link href="/play" onClick={startGame}>
                  <motion.button
                    whileHover={{
                      scale: 1.05,
                      boxShadow: '0 0 30px rgba(239, 68, 68, 0.6)'
                    }}
                    whileTap={{ scale: 0.95 }}
                    style={{
                      padding: '1rem 2rem',
                      background: 'linear-gradient(to right, #b91c1c, #7f1d1d)',
                      color: 'white',
                      fontSize: '1.25rem',
                      fontWeight: 'bold',
                      borderRadius: '0.5rem',
                      border: '2px solid #ef4444',
                      fontFamily: 'serif',
                      textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)',
                      boxShadow: '0 4px 15px rgba(0, 0, 0, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease'
                    }}
                  >
                    Face the Reaper
                  </motion.button>
                </Link>

                <Link href="/stats">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    style={{
                      color: '#9ca3af',
                      textDecoration: 'underline',
                      fontSize: '1.125rem',
                      fontFamily: 'serif',
                      textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)',
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      transition: 'color 0.3s ease'
                    }}
                  >
                    View Stats
                  </motion.button>
                </Link>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Atmospheric Particles Effect */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            initial={{
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
            style={{
              position: 'absolute',
              width: '4px',
              height: '4px',
              backgroundColor: '#f87171',
              borderRadius: '50%',
              opacity: 0.3,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
          />
        ))}
      </div>
    </div>
  );
}
