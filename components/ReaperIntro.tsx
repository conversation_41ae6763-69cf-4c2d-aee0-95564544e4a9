'use client';

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useGameStore } from '@/lib/gameStore';

const message = 'Welcome, mortal. Answer my riddles and steal back your years.';

export default function ReaperIntro() {
  const [text, setText] = useState('');
  const reset = useGameStore((s) => s.reset);

  useEffect(() => {
    let i = 0;
    const t = setInterval(() => {
      setText(message.slice(0, i));
      i++;
      if (i > message.length) clearInterval(t);
    }, 40);
    return () => clearInterval(t);
  }, []);

  const startGame = () => {
    reset();
  };

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="flex flex-col items-center justify-center min-h-screen text-center p-4 gap-6">
      <p className="text-xl max-w-xl">{text}</p>
      <Link href="/play" onClick={startGame} className="px-4 py-2 bg-purple-700 rounded">
        Face the Reaper
      </Link>
      <Link href="/stats" className="underline text-sm text-gray-400">
        View Stats
      </Link>
    </motion.div>
  );
}
