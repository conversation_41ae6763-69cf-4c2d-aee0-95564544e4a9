'use client';

interface Props {
  onGuess: (letter: string) => void;
  guessed: string[];
  disabled?: boolean;
}

const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');

export default function Keyboard({ onGuess, guessed, disabled }: Props) {
  return (
    <div className="grid grid-cols-7 gap-2">
      {letters.map((l) => (
        <button
          key={l}
          onClick={() => onGuess(l)}
          disabled={disabled || guessed.includes(l)}
          className="px-2 py-1 border rounded disabled:opacity-50"
        >
          {l}
        </button>
      ))}
    </div>
  );
}
