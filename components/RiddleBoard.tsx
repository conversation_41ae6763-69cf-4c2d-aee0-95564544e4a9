'use client';

import { motion, useAnimation } from 'framer-motion';
import { useEffect } from 'react';
import { Riddle } from '@/lib/riddles';

interface Props {
  riddle: Riddle;
  guessed: string[];
  wrongCount: number;
}

export default function RiddleBoard({ riddle, guessed, wrongCount }: Props) {
  const controls = useAnimation();

  useEffect(() => {
    if (wrongCount > 0) {
      controls.start({ x: [-10, 10, -10, 0], transition: { duration: 0.3 } });
    }
  }, [wrongCount, controls]);

  const masked = riddle.answer
    .toUpperCase()
    .split('')
    .map((ch) => (ch === ' ' ? ' ' : guessed.includes(ch) ? ch : '_'))
    .join(' ');

  return (
    <motion.div animate={controls} className="space-y-4 text-center">
      <p className="text-lg">{riddle.prompt}</p>
      <p className="text-3xl tracking-widest font-mono">{masked}</p>
    </motion.div>
  );
}
