#!/usr/bin/env node
const net = require('node:net');
const { spawn } = require('node:child_process');

const MIN_PORT = 3000;
const MAX_PORT = 3999;

function isPortFree(port) {
  return new Promise((resolve) => {
    const server = net.createServer();
    server.unref();
    server.on('error', () => resolve(false));
    server.listen({ port, host: '127.0.0.1', exclusive: true }, () => {
      server.close(() => resolve(true));
    });
  });
}

async function findPort() {
  const envPort = parseInt(process.env.PORT || '', 10);
  if (Number.isInteger(envPort) && envPort >= MIN_PORT && envPort <= MAX_PORT) {
    if (await isPortFree(envPort)) return envPort;
  }
  for (let p = MIN_PORT; p <= MAX_PORT; p++) {
    // Skip the envPort if it was set but busy
    if (p === envPort) continue;
    if (await isPortFree(p)) return p;
  }
  throw new Error(`No free ports between ${MIN_PORT}-${MAX_PORT}`);
}

async function main() {
  try {
    const port = await findPort();
    console.log(`Starting Next.js dev server on port ${port}...`);
    const args = ['dev', '-p', String(port), ...process.argv.slice(2)];
    const cmd = process.platform === 'win32' ? 'next.cmd' : 'next';
    const child = spawn(cmd, args, { stdio: 'inherit', env: process.env });

    // Forward termination signals to child so Next cleans up
    const forward = (signal) => () => child.kill(signal);
    process.on('SIGINT', forward('SIGINT'));
    process.on('SIGTERM', forward('SIGTERM'));

    child.on('exit', (code, signal) => {
      if (signal) {
        process.kill(process.pid, signal);
      } else {
        process.exit(code ?? 0);
      }
    });

    child.on('error', (err) => {
      console.error('Failed to start Next.js:', err.message);
      process.exit(1);
    });
  } catch (err) {
    console.error(String(err && err.message ? err.message : err));
    process.exit(1);
  }
}

main();

