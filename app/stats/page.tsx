'use client';

import { useEffect, useState } from 'react';
import { getStats, Stats } from '@/lib/storage';

export default function StatsPage() {
  const [stats, setStats] = useState<Stats | null>(null);

  useEffect(() => {
    getStats().then(setStats);
  }, []);

  if (!stats) return <main className="p-4">Loading...</main>;

  return (
    <main className="p-4 space-y-2">
      <h1 className="text-2xl mb-4">Stats</h1>
      <p>Games Played: {stats.gamesPlayed}</p>
      <p>Riddles Solved: {stats.riddlesSolved}</p>
      <p>Wrong Guesses: {stats.wrongGuesses}</p>
      <p>Years Earned: {stats.yearsEarned}</p>
      <p>Current Streak: {stats.currentStreak}</p>
      <p>Best Streak: {stats.bestStreak}</p>
    </main>
  );
}
