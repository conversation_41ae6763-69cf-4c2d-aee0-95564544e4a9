'use client';

import { useEffect } from 'react';
import { useGameStore } from '@/lib/gameStore';
import RiddleBoard from '@/components/RiddleBoard';
import Keyboard from '@/components/Keyboard';
import HUD from '@/components/HUD';

export default function PlayPage() {
  const { riddle, guessed, wrong, guess, status, reset } = useGameStore();

  useEffect(() => {
    if (status === 'intro') reset();
  }, [status, reset]);

  // handle physical keyboard input
  useEffect(() => {
    const handler = (e: KeyboardEvent) => {
      const letter = e.key.toUpperCase();
      if (/^[A-Z]$/.test(letter)) guess(letter);
    };
    window.addEventListener('keydown', handler);
    return () => window.removeEventListener('keydown', handler);
  }, [guess]);

  if (status === 'lost') {
    return (
      <main className="flex flex-col items-center justify-center gap-4 min-h-screen text-center">
        <p className="text-xl">Your time has run out.</p>
        <button onClick={reset} className="px-4 py-2 bg-purple-700 rounded">Try Again</button>
      </main>
    );
  }

  return (
    <main className="p-4 space-y-6">
      <HUD />
      <RiddleBoard riddle={riddle} guessed={guessed} wrongCount={wrong.length} />
      <Keyboard onGuess={guess} guessed={[...guessed, ...wrong]} />
    </main>
  );
}
